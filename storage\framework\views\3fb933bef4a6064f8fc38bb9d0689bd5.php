<?php $__env->startPush('scripts'); ?>
    <script>
        // Pass the category mappings from the server to JavaScript
        const legacyCategoryMapping = <?php echo json_encode($legacyCategoryMapping ?? [], 15, 512) ?>;
        const legacySubcategoryMapping = <?php echo json_encode($legacySubcategoryMapping ?? [], 15, 512) ?>;
    </script>
    <script src="<?php echo e(asset(js_path() . '/category-selector.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <div class="space-y-6">
        <div class="flex items-center gap-4">
            <a href="<?php echo e(route('seller.products.index', [], false) ?? '#'); ?>"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Add New
                    <?php if(isset($productType)): ?>
                        <?php switch($productType):
                            case ('ebook'): ?>
                                Ebook
                                <?php break; ?>
                            <?php case ('digital'): ?>
                                Digital Product
                                <?php break; ?>
                            <?php case ('course'): ?>
                                Course/Tutorial
                                <?php break; ?>
                            <?php default: ?>
                                Product
                        <?php endswitch; ?>
                    <?php else: ?>
                        Product
                    <?php endif; ?>
                </h1>
                <p class="text-gray-600">
                    <?php if(isset($productType)): ?>
                        <?php switch($productType):
                            case ('ebook'): ?>
                                Create a digital book, guide, or written content for download
                                <?php break; ?>
                            <?php case ('digital'): ?>
                                Create a digital product like software, templates, or tools
                                <?php break; ?>
                            <?php case ('course'): ?>
                                Create a structured course with lessons and educational materials
                                <?php break; ?>
                            <?php default: ?>
                                Create and publish a new digital product
                        <?php endswitch; ?>
                    <?php else: ?>
                        Create and publish a new digital product
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Product Type Indicator -->
        <?php if(isset($productType)): ?>
            <div class="rounded-xl border bg-white shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="rounded-full p-2
                            <?php switch($productType):
                                case ('ebook'): ?> bg-blue-100 <?php break; ?>
                                <?php case ('digital'): ?> bg-green-100 <?php break; ?>
                                <?php case ('course'): ?> bg-purple-100 <?php break; ?>
                            <?php endswitch; ?>">
                            <svg class="h-5 w-5
                                <?php switch($productType):
                                    case ('ebook'): ?> text-blue-600 <?php break; ?>
                                    <?php case ('digital'): ?> text-green-600 <?php break; ?>
                                    <?php case ('course'): ?> text-purple-600 <?php break; ?>
                                <?php endswitch; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <?php switch($productType):
                                    case ('ebook'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                        <?php break; ?>
                                    <?php case ('digital'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                        <?php break; ?>
                                    <?php case ('course'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                        <?php break; ?>
                                <?php endswitch; ?>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">Product Type:
                                <?php switch($productType):
                                    case ('ebook'): ?> Ebook <?php break; ?>
                                    <?php case ('digital'): ?> Digital Product <?php break; ?>
                                    <?php case ('course'): ?> Course/Tutorial <?php break; ?>
                                <?php endswitch; ?>
                            </h3>
                            <p class="text-xs text-gray-500">
                                <?php switch($productType):
                                    case ('ebook'): ?> Digital books and written content <?php break; ?>
                                    <?php case ('digital'): ?> Software, templates, and digital tools <?php break; ?>
                                    <?php case ('course'): ?> Structured learning with lessons <?php break; ?>
                                <?php endswitch; ?>
                            </p>
                        </div>
                    </div>
                    <a href="<?php echo e(route('seller.products.select-type')); ?>"
                        class="text-sm text-indigo-600 hover:text-indigo-500">Change Type</a>
                </div>
            </div>
        <?php endif; ?>

        <form id="product-form" action="<?php echo e(route('seller.products.store', [], false) ?? '#'); ?>" method="POST"
            enctype="multipart/form-data" onsubmit="if(typeof tinyMCE !== 'undefined') { tinyMCE.triggerSave(); }">
            <?php echo csrf_field(); ?>

            <!-- Hidden fields for product type -->
            <?php if(isset($productType)): ?>
                <input type="hidden" name="product_type" value="<?php echo e($productType); ?>">
            <?php endif; ?>
            <?php if(isset($contentType)): ?>
                <input type="hidden" name="content_type" value="<?php echo e($contentType); ?>">
            <?php endif; ?>
            <div class="grid gap-6 md:grid-cols-6">
                <div class="space-y-6 md:col-span-4">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Information</h3>
                            <p class="text-sm text-gray-600">Basic information about your digital product</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">
                                    Product Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="e.g. Financial Planning Spreadsheet" value="<?php echo e(old('name')); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="space-y-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" id="description"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="Describe your product in detail..." required><?php echo e(old('description')); ?></textarea>
                                <p class="text-xs text-gray-500">
                                    Provide a detailed description of your product, including its features and benefits.
                                </p>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="space-y-2">
                                <label for="category" class="block text-sm font-medium text-gray-700">
                                    Legacy Category <span class="text-red-500">*</span>
                                </label>
                                <select name="category" id="category"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    required>
                                    <option value="">Select a category</option>
                                    <?php $__currentLoopData = $categoryGroups ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupName => $categories): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <optgroup label="<?php echo e($groupName); ?>">
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($value); ?>"
                                                    <?php echo e(old('category') == $value ? 'selected' : ''); ?>><?php echo e($label); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </optgroup>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="space-y-2">
                                <label for="category_id" class="block text-sm font-medium text-gray-700">
                                    Detailed Category <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="text" id="category_search"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                        placeholder="Search for a category..." autocomplete="off">
                                    <div id="category_dropdown"
                                        class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 hidden max-h-60 overflow-y-auto">
                                        <div class="p-2">
                                            <?php $__currentLoopData = $categoryTree ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $topCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="category-group mb-2">
                                                    <div class="font-medium text-gray-700 py-1"><?php echo e($topCategory->name); ?>

                                                    </div>
                                                    <?php $__currentLoopData = $topCategory->activeSubcategories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="pl-3 mb-1">
                                                            <div class="font-medium text-gray-600 py-1">
                                                                <?php echo e($subCategory->name); ?></div>
                                                            <?php $__currentLoopData = $subCategory->activeDetailedCategories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detailedCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="pl-3">
                                                                    <div class="category-option py-1 px-2 hover:bg-gray-100 rounded cursor-pointer"
                                                                        data-id="<?php echo e($detailedCategory->id); ?>"
                                                                        data-category-id="<?php echo e($topCategory->id); ?>"
                                                                        data-subcategory-id="<?php echo e($subCategory->id); ?>"
                                                                        data-name="<?php echo e($topCategory->name); ?> > <?php echo e($subCategory->name); ?> > <?php echo e($detailedCategory->name); ?>">
                                                                        <?php echo e($detailedCategory->name); ?>

                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="category_id" id="category_id" value="<?php echo e(old('category_id')); ?>">
                                <input type="hidden" name="subcategory_id" id="subcategory_id"
                                    value="<?php echo e(old('subcategory_id')); ?>">
                                <input type="hidden" name="detailed_category_id" id="detailed_category_id"
                                    value="<?php echo e(old('detailed_category_id')); ?>">
                                <div id="selected_category"
                                    class="mt-2 p-2 bg-gray-50 rounded-md <?php echo e(old('detailed_category_id') ? '' : 'hidden'); ?>">
                                    <div class="flex items-center justify-between">
                                        <span id="selected_category_name" class="text-sm text-gray-700"></span>
                                        <button type="button" id="clear_category"
                                            class="text-sm text-red-500 hover:text-red-700">
                                            Clear
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">
                                    Select the most specific category for your product to help buyers find it.
                                </p>
                                <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Images <span
                                    class="text-red-500">*</span></h3>
                            <p class="text-sm text-gray-600">Upload images for your product (required when publishing). The
                                first image will be used as the cover.</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    <p class="text-sm font-medium text-gray-700">Drag and drop images here or click to
                                        browse</p>
                                    <p class="text-xs text-gray-500">
                                        Upload product images (JPEG, PNG, JPG, GIF, max 2MB each, up to 10 images)
                                    </p>
                                    <input id="images" name="images[]" type="file" class="hidden"
                                        accept="image/jpeg,image/png,image/jpg,image/gif" multiple>
                                    <button type="button"
                                        class="choose-images mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Choose Images
                                    </button>
                                </div>
                            </div>
                            <div id="images-preview" class="space-y-2 hidden">
                                <div class="flex justify-between items-center">
                                    <label class="block text-sm font-medium text-gray-700">Uploaded Images</label>
                                    <p class="text-xs text-gray-500">Drag to reorder. First image is the cover.</p>
                                </div>
                                <div id="images-list" class="space-y-2"></div>
                            </div>
                            <?php $__errorArgs = ['images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium"><?php echo e($message); ?></span>
                                    </div>
                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium"><?php echo e($message); ?></span>
                                    </div>
                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Hidden input for content type - always simple for digital products -->
                    <input type="hidden" name="content_type" value="simple">

                    <!-- Product Files Section -->
                    <div class="mt-6">
                                    <div class="rounded-xl border bg-white shadow-lg">
                                        <div class="border-b border-gray-100 p-6">
                                            <h3 class="text-lg font-semibold text-gray-900">
                                                <?php if(isset($productType)): ?>
                                                    <?php switch($productType):
                                                        case ('ebook'): ?>
                                                            Ebook Files
                                                            <?php break; ?>
                                                        <?php case ('digital'): ?>
                                                            Digital Product Files
                                                            <?php break; ?>
                                                        <?php default: ?>
                                                            Product Files
                                                    <?php endswitch; ?>
                                                <?php else: ?>
                                                    Product Files
                                                <?php endif; ?>
                                                <span class="text-red-500">*</span>
                                            </h3>
                                            <p class="text-sm text-gray-600">
                                                <?php if(isset($productType)): ?>
                                                    <?php switch($productType):
                                                        case ('ebook'): ?>
                                                            Upload your ebook files (PDF, EPUB, DOC, etc.) that customers will download. Maximum 5 files, 20MB each.
                                                            <?php break; ?>
                                                        <?php case ('digital'): ?>
                                                            Upload your digital product files (software, templates, tools, etc.) that customers will download. Maximum 5 files, 20MB each.
                                                            <?php break; ?>
                                                        <?php default: ?>
                                                            Upload the files that customers will receive (required when publishing). Maximum 5 files, 20MB each.
                                                    <?php endswitch; ?>
                                                <?php else: ?>
                                                    Upload the files that customers will receive (required when publishing). Maximum 5 files, 20MB each.
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <div class="p-6 space-y-5">
                                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                        <polyline points="17 8 12 3 7 8"></polyline>
                                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                                    </svg>
                                                    <p class="text-sm font-medium text-gray-700">Drag and drop files here or click to
                                                        browse</p>
                                                    <p class="text-xs text-gray-500">
                                                        <?php if(isset($productType) && $productType === 'ebook'): ?>
                                                            Upload your ebook files (PDF, EPUB, MOBI, DOC, DOCX, TXT, ZIP, max 20MB each, up to 5 files)
                                                        <?php else: ?>
                                                            Upload the files that customers will download after purchase (ZIP, PDF, DOCX, XLSX, PPTX, TXT, max 20MB each, up to 5 files)
                                                        <?php endif; ?>
                                                    </p>
                                                    <input id="files" name="files[]" type="file" class="hidden" multiple
                                                        accept=".zip,.pdf,.epub,.docx,.doc,.xlsx,.pptx,.txt,.mobi,.azw,.azw3">
                                                    <button type="button"
                                                        class="choose-files mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                                        Choose Files
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- File Format Guidelines -->
                                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                                                <h4 class="font-medium text-blue-800 mb-2">File Format Guidelines</h4>
                                                <ul class="text-sm text-blue-700 space-y-2">
                                                    <li class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            class="h-5 w-5 flex-shrink-0 mt-0.5 text-blue-500">
                                                            <polyline points="9 11 12 14 22 4"></polyline>
                                                            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                                                        </svg>
                                                        <span><strong>Design Files (SVG, AI, PSD, etc.):</strong> Compress these files into
                                                            a ZIP archive before uploading. Include a README file with usage
                                                            instructions.</span>
                                                    </li>
                                                    <li class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            class="h-5 w-5 flex-shrink-0 mt-0.5 text-blue-500">
                                                            <polyline points="9 11 12 14 22 4"></polyline>
                                                            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                                                        </svg>
                                                        <span><strong>Multiple Files:</strong> For products with multiple components,
                                                            organize them in folders and compress into a single ZIP file.</span>
                                                    </li>
                                                    <li class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            class="h-5 w-5 flex-shrink-0 mt-0.5 text-blue-500">
                                                            <polyline points="9 11 12 14 22 4"></polyline>
                                                            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                                                        </svg>
                                                        <span><strong>Documentation:</strong> Always include a usage guide or documentation
                                                            file explaining how to use your product.</span>
                                                    </li>
                                                </ul>
                                            </div>

                                            <div id="file-preview" class="space-y-2 hidden">
                                                <label class="block text-sm font-medium text-gray-700">Uploaded Files</label>
                                                <div id="file-list" class="space-y-2"></div>
                                            </div>
                                            <?php $__errorArgs = ['files'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                                    <div class="flex items-center gap-2 text-red-600">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                                            <circle cx="12" cy="12" r="10"></circle>
                                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                                        </svg>
                                                        <span class="text-sm font-medium"><?php echo e($message); ?></span>
                                                    </div>
                                                </div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <?php $__errorArgs = ['files.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                                    <div class="flex items-center gap-2 text-red-600">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                                            <circle cx="12" cy="12" r="10"></circle>
                                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                                        </svg>
                                                        <span class="text-sm font-medium"><?php echo e($message); ?></span>
                                                    </div>
                                                </div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>



                </div>

                <div class="space-y-6 md:col-span-2">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                            <p class="text-sm text-gray-600">Set your product pricing</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-medium text-gray-700">
                                    Price (Rp) <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="price" id="price" step="0.01" min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="29999" value="<?php echo e(old('price')); ?>" required>
                                <p class="mt-1 text-xs text-gray-500">Minimum price is Rp 5,000</p>
                                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" name="has_discount" id="has_discount"
                                    class="rounded border-gray-200 text-indigo-600 focus:ring-indigo-500">
                                <label for="has_discount" class="text-sm font-medium text-gray-700">Enable discount
                                    price</label>
                            </div>
                            <div id="discount-price-container"
                                class="space-y-2 <?php echo e(old('has_discount') ? '' : 'hidden'); ?>">
                                <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price
                                    (Rp)</label>
                                <input type="number" name="discount_price" id="discount_price" step="0.01"
                                    min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="19999" value="<?php echo e(old('discount_price')); ?>">
                                <p class="mt-1 text-xs text-gray-500">Minimum discount price is Rp 5,000</p>
                                <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- AI Chatbot Configuration -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">AI Chatbot</h3>
                            <p class="text-sm text-gray-600">Configure AI assistant for this product</p>
                        </div>
                        <div class="p-6">
                            <?php
                                $canActivateChatbot = Auth::user()->canActivateChatbot();
                                $currentTier = Auth::user()->getCurrentMembershipTier();
                            ?>

                            <div class="space-y-4">
                                <?php if($currentTier && ($currentTier->chatbot_products_limit > 0 || $currentTier->chatbot_products_limit === -1)): ?>
                                    <!-- Membership Status Display -->
                                    <div class="bg-gradient-to-r <?php echo e($currentTier->slug === 'basic' ? 'from-gray-50 to-gray-100 border-gray-300' : 'from-yellow-50 to-yellow-100 border-yellow-300'); ?> border-2 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <?php if($currentTier->slug === 'basic'): ?>
                                                    <!-- Silver Badge -->
                                                    <div class="flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-gray-400 to-gray-600 text-white text-xs font-bold mr-3">
                                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                        </svg>
                                                        SILVER
                                                    </div>
                                                <?php else: ?>
                                                    <!-- Gold Badge -->
                                                    <div class="flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 text-xs font-bold mr-3">
                                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                        </svg>
                                                        GOLD
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <p class="text-sm font-semibold text-gray-900"><?php echo e($currentTier->name); ?> Membership Active</p>
                                                    <p class="text-xs text-gray-600">
                                                        Chatbot Limit: <?php echo e($currentTier->chatbot_products_limit === -1 ? 'Unlimited' : $currentTier->chatbot_products_limit); ?> products
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chatbot Features -->
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-blue-900 mb-2">AI Chatbot Available ✨</p>
                                                <p class="text-xs text-blue-700 mb-3">After creating your product, you'll be able to configure an intelligent AI assistant that can:</p>
                                                <ul class="text-xs text-blue-600 space-y-1 ml-4">
                                                    <li>• Answer customer questions instantly</li>
                                                    <li>• Provide detailed product information</li>
                                                    <li>• Help with technical support</li>
                                                    <li>• Increase customer engagement and sales</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if(!$canActivateChatbot): ?>
                                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                            </svg>
                                            <div>
                                                <p class="text-sm font-medium text-amber-800">Chatbot Limit Reached</p>
                                                <p class="text-xs text-amber-700">
                                                    You've used <?php echo e($currentTier->chatbot_products_limit); ?> of <?php echo e($currentTier->chatbot_products_limit); ?> available chatbots.
                                                    <a href="<?php echo e(route('membership.index')); ?>" class="underline hover:text-amber-900 font-medium">Upgrade to Pro</a>
                                                    for unlimited chatbots.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-4 text-gray-400">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <h4 class="text-lg font-semibold text-gray-900 mb-2">AI Chatbot Not Available</h4>
                                        <p class="text-sm text-gray-600 mb-4">
                                            Upgrade your membership to unlock AI-powered chatbots for your products
                                        </p>
                                        <div class="bg-white rounded-lg p-4 mb-4 border border-gray-200">
                                            <p class="text-xs text-gray-500 mb-2">Current tier: <span class="font-medium"><?php echo e($currentTier ? $currentTier->name : 'Starter (Free)'); ?></span></p>
                                            <p class="text-xs text-gray-500">Chatbot access: <span class="text-red-600 font-medium">Not included</span></p>
                                        </div>
                                        <a href="<?php echo e(route('membership.index')); ?>"
                                           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            Upgrade Membership
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Publishing</h3>
                            <p class="text-sm text-gray-600">Control your product visibility</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div x-data="{ status: '<?php echo e(old('status', 'draft')); ?>' }">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" x-model="status"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                        <option value="draft">Draft</option>
                                        <option value="active">Published</option>
                                    </select>
                                </div>
                                <div x-show="status === 'draft'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Save as draft to continue working on it later. It won't be visible to customers.
                                    </p>
                                </div>
                                <div x-show="status === 'active'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Publish your product to make it available for purchase immediately.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-3 pt-4">
                                    <button type="submit"
                                        :class="{
                                            'bg-amber-600 hover:bg-amber-700': status === 'draft',
                                            'bg-emerald-600 hover:bg-emerald-700': status === 'active'
                                        }"
                                        class="inline-flex w-full items-center justify-center rounded-lg px-4 py-2 text-sm font-medium text-white shadow-md transition-colors">
                                        <span x-text="status === 'active' ? 'Publish Product' : 'Save as Draft'"></span>
                                    </button>
                                    <a href="<?php echo e(route('seller.products.index', [], false) ?? '#'); ?>"
                                        class="inline-flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <?php $__env->startPush('scripts'); ?>
        
        <script src="<?php echo e(asset('tinymce/tinymce.min.js')); ?>"></script>
        
        <script src="<?php echo e(asset('dev-js/product.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/products/create.blade.php ENDPATH**/ ?>